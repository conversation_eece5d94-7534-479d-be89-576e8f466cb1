import 'package:flutter/material.dart';
import 'dart:developer' as developer;
import 'package:konnect/screens/events_screen.dart';
import 'package:konnect/screens/home_screen.dart';
import 'package:konnect/screens/peer_connect_screen.dart';
import 'package:konnect/widgets/bottom_navigation.dart';
import 'package:konnect/models/housing.dart';
import 'package:konnect/services/housing_service.dart';
import 'package:konnect/screens/upskill_screen.dart';
import 'package:konnect/screens/shop_screen.dart';
import 'package:konnect/utils/skeleton_theme.dart';

class MapScreen extends StatefulWidget {
  final bool showNavBar;
  final int currentIndex;

  const MapScreen({super.key, this.showNavBar = true, this.currentIndex = 1});

  @override
  State<MapScreen> createState() => _MapScreenState();
}

class _MapScreenState extends State<MapScreen> {
  // Map-related state variables
  final _mapContent = const Center(child: Text('Map Screen Content'));

  bool _showHousingListings = false;
  List<Housing> _housingListings = [];
  bool _isLoading = false;
  String? _errorMessage;

  // Filter state
  double _minRent = 0;
  double _maxRent = 3000;
  int _minBeds = 0;
  int _maxBeds = 5;
  bool? _isFurnished;

  final HousingService _housingService = HousingService();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Campus Map'),
        automaticallyImplyLeading: true,
        actions: [
          if (_showHousingListings)
            IconButton(
              icon: const Icon(Icons.filter_list),
              onPressed: _showFilterDialog,
              tooltip: 'Filter Listings',
            ),
          if (_showHousingListings)
            IconButton(
              icon: const Icon(Icons.map),
              onPressed: () {
                setState(() {
                  _showHousingListings = false;
                  _errorMessage = null;
                });
              },
              tooltip: 'Back to Map',
            ),
        ],
      ),
      body: _showHousingListings ? _buildHousingListings() : _buildMapContent(),
      bottomNavigationBar: BottomNavigation(
        currentIndex: 1,
        onTap: (index) {
          if (index != 1) {
            _onBottomNavTap(index);
          }
        },
      ),
    );
  }

  Widget _buildMapContent() {
    return Column(
      children: [
        _mapContent,
        const SizedBox(height: 20),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20.0),
          child: Row(
            children: [
              Expanded(
                child: _buildActionButton(
                  'Navigate School',
                  Icons.navigation,
                  () {
                    // School navigation functionality
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Campus navigation coming soon'),
                      ),
                    );
                  },
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildActionButton('House Hunting', Icons.house, () {
                  _fetchHousingListings();
                }),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildActionButton(
    String label,
    IconData icon,
    VoidCallback onPressed,
  ) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: const Color(0xFF2A9D8F),
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(vertical: 16.0),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 28),
          const SizedBox(height: 8),
          Text(label),
        ],
      ),
    );
  }

  Future<void> _fetchHousingListings() async {
    setState(() {
      _isLoading = true;
      _showHousingListings = true;
      _errorMessage = null;
    });

    try {
      developer.log('MapScreen: Fetching housing listings');
      final listings = await _housingService.getAllHousing();
      developer.log('MapScreen: Received ${listings.length} listings');

      setState(() {
        _housingListings = listings;
        _isLoading = false;
      });
    } catch (e) {
      developer.log('MapScreen: Error fetching housing listings: $e');
      setState(() {
        _errorMessage = 'Failed to load housing listings: $e';
        _isLoading = false;
      });
    }
  }

  // Add a method to search with filters
  Future<void> _searchHousingWithFilters() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final listings = await _housingService.searchHousing(
        minRent: _minRent > 0 ? _minRent : null,
        maxRent: _maxRent > 0 ? _maxRent : null,
        minBeds: _minBeds > 0 ? _minBeds : null,
        maxBeds: _maxBeds > 0 ? _maxBeds : null,
        furnished: _isFurnished,
      );
      setState(() {
        _housingListings = listings;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to search housing listings: $e';
        _isLoading = false;
      });
    }
  }

  // Add a filter dialog
  void _showFilterDialog() {
    showDialog(
      context: context,
      builder:
          (context) => StatefulBuilder(
            builder: (context, setDialogState) {
              return AlertDialog(
                title: const Text('Filter Housing'),
                content: SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Text('Price Range'),
                      RangeSlider(
                        values: RangeValues(_minRent, _maxRent),
                        min: 0,
                        max: 3000,
                        divisions: 30,
                        labels: RangeLabels(
                          '\$${_minRent.round()}',
                          '\$${_maxRent.round()}',
                        ),
                        onChanged: (values) {
                          setDialogState(() {
                            _minRent = values.start;
                            _maxRent = values.end;
                          });
                        },
                      ),
                      const SizedBox(height: 16),
                      const Text('Bedrooms'),
                      RangeSlider(
                        values: RangeValues(
                          _minBeds.toDouble(),
                          _maxBeds.toDouble(),
                        ),
                        min: 0,
                        max: 5,
                        divisions: 5,
                        labels: RangeLabels(
                          '${_minBeds.round()}',
                          '${_maxBeds.round()}',
                        ),
                        onChanged: (values) {
                          setDialogState(() {
                            _minBeds = values.start.round();
                            _maxBeds = values.end.round();
                          });
                        },
                      ),
                      const SizedBox(height: 16),
                      Row(
                        children: [
                          const Text('Furnished:'),
                          const SizedBox(width: 8),
                          DropdownButton<bool?>(
                            value: _isFurnished,
                            hint: const Text('Any'),
                            items: const [
                              DropdownMenuItem(value: null, child: Text('Any')),
                              DropdownMenuItem(value: true, child: Text('Yes')),
                              DropdownMenuItem(value: false, child: Text('No')),
                            ],
                            onChanged: (value) {
                              setDialogState(() {
                                _isFurnished = value;
                              });
                            },
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                actions: [
                  TextButton(
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    child: const Text('Cancel'),
                  ),
                  TextButton(
                    onPressed: () {
                      Navigator.pop(context);
                      _searchHousingWithFilters();
                    },
                    child: const Text('Apply'),
                  ),
                ],
              );
            },
          ),
    );
  }

  Widget _buildHousingListings() {
    return RefreshIndicator(
      onRefresh: _refreshHousingListings,
      child:
          _isLoading
              ? SkeletonTheme.create(
                child: ListView.builder(
                  padding: const EdgeInsets.all(16.0),
                  itemCount: 3, // Show 3 skeleton items
                  itemBuilder: (context, index) {
                    return Card(
                      margin: const EdgeInsets.only(bottom: 16.0),
                      clipBehavior: Clip.antiAlias,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                      ),
                      elevation: 2,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Property image skeleton
                          Container(
                            height: 180,
                            width: double.infinity,
                            color: Colors.grey[300],
                          ),

                          Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // Title and availability tag
                                Row(
                                  children: [
                                    Expanded(
                                      child: Container(
                                        height: 24,
                                        width: 200,
                                        decoration: BoxDecoration(
                                          color: Colors.grey[300],
                                          borderRadius: BorderRadius.circular(
                                            4,
                                          ),
                                        ),
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    Container(
                                      height: 20,
                                      width: 80,
                                      decoration: BoxDecoration(
                                        color: Colors.grey[300],
                                        borderRadius: BorderRadius.circular(4),
                                      ),
                                    ),
                                  ],
                                ),

                                const SizedBox(height: 8),

                                // Address
                                Container(
                                  height: 16,
                                  width: double.infinity,
                                  decoration: BoxDecoration(
                                    color: Colors.grey[300],
                                    borderRadius: BorderRadius.circular(4),
                                  ),
                                ),

                                const SizedBox(height: 16),

                                // Rent amount
                                Container(
                                  height: 24,
                                  width: 120,
                                  decoration: BoxDecoration(
                                    color: Colors.grey[300],
                                    borderRadius: BorderRadius.circular(4),
                                  ),
                                ),

                                const SizedBox(height: 16),

                                // Property details
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceAround,
                                  children: List.generate(
                                    4,
                                    (i) => Column(
                                      children: [
                                        Container(
                                          height: 20,
                                          width: 20,
                                          decoration: BoxDecoration(
                                            color: Colors.grey[300],
                                            shape: BoxShape.circle,
                                          ),
                                        ),
                                        const SizedBox(height: 4),
                                        Container(
                                          height: 12,
                                          width: 40,
                                          decoration: BoxDecoration(
                                            color: Colors.grey[300],
                                            borderRadius: BorderRadius.circular(
                                              4,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),

                                const SizedBox(height: 16),

                                // Amenities
                                Wrap(
                                  spacing: 8,
                                  runSpacing: 8,
                                  children: List.generate(
                                    3,
                                    (i) => Container(
                                      height: 24,
                                      width: 80,
                                      decoration: BoxDecoration(
                                        color: Colors.grey[300],
                                        borderRadius: BorderRadius.circular(16),
                                      ),
                                    ),
                                  ),
                                ),

                                const SizedBox(height: 16),

                                // Contact button
                                Container(
                                  height: 48,
                                  width: double.infinity,
                                  decoration: BoxDecoration(
                                    color: Colors.grey[300],
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                ),

                                const SizedBox(height: 8),

                                // View details button
                                Container(
                                  height: 48,
                                  width: double.infinity,
                                  decoration: BoxDecoration(
                                    color: Colors.grey[300],
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              )
              : _errorMessage != null
              ? ListView(
                children: [
                  SizedBox(
                    height: MediaQuery.of(context).size.height / 3,
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            _errorMessage!,
                            textAlign: TextAlign.center,
                            style: const TextStyle(color: Colors.red),
                          ),
                          const SizedBox(height: 16),
                          ElevatedButton(
                            onPressed: _fetchHousingListings,
                            child: const Text('Retry'),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              )
              : _housingListings.isEmpty
              ? ListView(
                children: [
                  SizedBox(
                    height: MediaQuery.of(context).size.height / 3,
                    child: const Center(
                      child: Text('No housing listings found'),
                    ),
                  ),
                ],
              )
              : ListView.builder(
                padding: const EdgeInsets.all(16.0),
                itemCount: _housingListings.length,
                itemBuilder: (context, index) {
                  final housing = _housingListings[index];
                  return _buildHousingCard(housing);
                },
              ),
    );
  }

  Widget _buildHousingCard(Housing housing) {
    // Extract amenities and limit to 3-4 most important ones
    final amenities = housing.amenities.take(3).toList();

    return Card(
      margin: const EdgeInsets.only(bottom: 16.0),
      clipBehavior: Clip.antiAlias,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      elevation: 2,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Property image
          Container(
            height: 180,
            width: double.infinity,
            color: Colors.grey[300],
            child:
                housing.images.isNotEmpty
                    ? Image.network(
                      housing.images[0],
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return const Center(
                          child: Icon(Icons.home, size: 64, color: Colors.grey),
                        );
                      },
                    )
                    : const Center(
                      child: Icon(Icons.home, size: 64, color: Colors.grey),
                    ),
          ),

          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Title and availability tag
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        housing.name,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: const Color(0xFF2A9D8F).withOpacity(0.1),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        housing.availability,
                        style: const TextStyle(
                          color: Color(0xFF2A9D8F),
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 8),

                // Address
                Row(
                  children: [
                    const Icon(Icons.location_on, size: 16, color: Colors.grey),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        housing.address,
                        style: TextStyle(color: Colors.grey[600]),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 16),

                // Rent amount
                Text(
                  '\$${housing.rent.toStringAsFixed(0)}/month',
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF2A9D8F),
                  ),
                ),

                const SizedBox(height: 16),

                // Property details
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    _buildPropertyDetail(
                      Icons.king_bed,
                      '${housing.bedrooms} bed',
                    ),
                    _buildPropertyDetail(
                      Icons.bathtub,
                      '${housing.bathrooms} bath',
                    ),
                    _buildPropertyDetail(
                      Icons.square_foot,
                      '${housing.squareFeet} sqft',
                    ),
                    _buildPropertyDetail(
                      Icons.chair,
                      housing.isFurnished ? 'Furnished' : 'Unfurnished',
                    ),
                  ],
                ),

                const SizedBox(height: 16),

                // Amenities
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children:
                      amenities.map((amenity) {
                        return Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 10,
                            vertical: 6,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.grey[200],
                            borderRadius: BorderRadius.circular(16),
                          ),
                          child: Text(
                            amenity,
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[800],
                            ),
                          ),
                        );
                      }).toList(),
                ),

                const SizedBox(height: 16),

                // Contact button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () {
                      // Contact functionality
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(
                            'Contact ${housing.contactName} at ${housing.contactPhone}',
                          ),
                        ),
                      );
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF2A9D8F),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                    child: const Text('Contact Landlord'),
                  ),
                ),

                const SizedBox(height: 8),

                // View details button
                SizedBox(
                  width: double.infinity,
                  child: OutlinedButton(
                    onPressed: () {
                      _showHousingDetails(housing);
                    },
                    style: OutlinedButton.styleFrom(
                      side: const BorderSide(color: Color(0xFF2A9D8F)),
                      foregroundColor: const Color(0xFF2A9D8F),
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                    child: const Text('View Details'),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPropertyDetail(IconData icon, String text) {
    return Column(
      children: [
        Icon(icon, size: 20, color: Colors.grey[600]),
        const SizedBox(height: 4),
        Text(text, style: TextStyle(fontSize: 12, color: Colors.grey[600])),
      ],
    );
  }

  // Add a method to show housing details with skeleton loading
  void _showHousingDetails(Housing housing) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) {
        return DraggableScrollableSheet(
          initialChildSize: 0.5,
          maxChildSize: 0.8,
          builder: (context, scrollController) {
            return Container(
              padding: const EdgeInsets.all(16.0),
              child: SingleChildScrollView(
                controller: scrollController,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      housing.name,
                      style: const TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      housing.address,
                      style: TextStyle(color: Colors.grey[600]),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Description: ${housing.description}',
                      style: TextStyle(color: Colors.grey[800]),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Rent: \$${housing.rent.toStringAsFixed(0)}/month',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF2A9D8F),
                      ),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Property Details',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        _buildPropertyDetail(
                          Icons.king_bed,
                          '${housing.bedrooms} bed',
                        ),
                        _buildPropertyDetail(
                          Icons.bathtub,
                          '${housing.bathrooms} bath',
                        ),
                        _buildPropertyDetail(
                          Icons.square_foot,
                          '${housing.squareFeet} sqft',
                        ),
                        _buildPropertyDetail(
                          Icons.chair,
                          housing.isFurnished ? 'Furnished' : 'Unfurnished',
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Amenities',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children:
                          housing.amenities.map((amenity) {
                            return Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 10,
                                vertical: 6,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.grey[200],
                                borderRadius: BorderRadius.circular(16),
                              ),
                              child: Text(
                                amenity,
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey[800],
                                ),
                              ),
                            );
                          }).toList(),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Contact Information',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Name: ${housing.contactName}',
                      style: TextStyle(color: Colors.grey[800]),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Email: ${housing.contactEmail}',
                      style: TextStyle(color: Colors.grey[800]),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Phone: ${housing.contactPhone}',
                      style: TextStyle(color: Colors.grey[800]),
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  void _onBottomNavTap(int index) {
    if (index == widget.currentIndex) return;

    try {
      switch (index) {
        case 0:
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(builder: (context) => const HomeScreen()),
          );
          break;
        case 1:
          // Already on map screen
          break;
        case 2:
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(
              builder: (context) => const EventsScreen(currentIndex: 2),
            ),
          );
          break;
        case 3:
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(
              builder: (context) => const PeerConnectScreen(currentIndex: 3),
            ),
          );
          break;
        case 4:
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(
              builder: (context) => const UpskillScreen(currentIndex: 4),
            ),
          );
          break;
        case 5:
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(
              builder: (context) => const ShopScreen(currentIndex: 5),
            ),
          );
          break;
      }
    } catch (e) {
      print('Navigation error: $e');
      // Fallback to named route
      final routes = [
        '/home',
        '/map',
        '/events',
        '/connect',
        '/upskill',
        '/shop',
      ];
      if (index < routes.length) {
        Navigator.pushReplacementNamed(context, routes[index]);
      }
    }
  }

  // Add a method to refresh housing listings
  Future<void> _refreshHousingListings() async {
    developer.log('MapScreen: Refreshing housing listings');

    // Don't show loading indicator during refresh
    setState(() {
      _errorMessage = null;
    });

    try {
      // Use forceRefresh to bypass any caching
      final listings = await _housingService.getAllHousing(forceRefresh: true);
      developer.log(
        'MapScreen: Refresh complete, received ${listings.length} listings',
      );

      if (mounted) {
        setState(() {
          _housingListings = listings;
        });

        // Show a snackbar to confirm refresh
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Housing listings refreshed'),
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      developer.log('MapScreen: Error refreshing housing listings: $e');
      if (mounted) {
        setState(() {
          _errorMessage = 'Failed to refresh housing listings: $e';
        });
      }
    }
  }
}
